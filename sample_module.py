"""
Minimal sample_module used by unit tests.
Provides Calculator, add_numbers, divide_numbers with simple behavior.
"""

from __future__ import annotations


class Calculator:
    def __init__(self, precision: int = 2):
        self._precision = precision

    def multiply(self, a, b):
        return a * b

    def get_precision(self):
        return self._precision


def add_numbers(a, b):
    return a + b


def divide_numbers(a, b):
    if b == 0:
        raise ZeroDivisionError("division by zero")
    return a / b