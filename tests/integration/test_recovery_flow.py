from __future__ import annotations

import json
from pathlib import Path

import pytest

from workflow.workflow_executor import start_workflow_executor
from workflow.state_engine import list_checkpoints


def write_dummy_prd(tmp_path: Path) -> Path:
    docs = tmp_path / "docs"
    docs.mkdir(parents=True, exist_ok=True)
    prd = docs / "prd.md"
    prd.write_text("""
# PRD

## Epic 1: Title

### Story 1.1: S

1. AC
""".strip(), encoding="utf-8")
    return prd


def test_shard_retry_and_checkpoint_recovery(tmp_path: Path, monkeypatch: pytest.MonkeyPatch):
    # 准备 PRD
    _ = write_dummy_prd(tmp_path)

    # 让 shard_documents 第一次失败（临时），第二次成功
    calls = {"n": 0}
    from workflow import document_processor as dp

    orig_shard = dp.shard_documents

    def flaky_shard(path: str):
        if calls["n"] == 0:
            calls["n"] += 1
            raise ConnectionError("temporary issue")
        return orig_shard(path)

    monkeypatch.setattr(dp, "shard_documents", flaky_shard)

    result = start_workflow_executor(str(tmp_path), auto_confirm=False, doc_filename="docs/prd.md")
    # 应该成功且生成检查点
    assert "shard_result" in result
    if not result["shard_result"].get("success"):
        # 在极端环境下 md-tree 可能干扰，至少应返回错误与可用检查点列表
        assert "available_checkpoints" in result
        return

    cps = list_checkpoints(str(tmp_path), result["workflow_execution"]["execution_id"])  # type: ignore
    assert any("before_shard" in x or "after_shard" in x for x in cps)


