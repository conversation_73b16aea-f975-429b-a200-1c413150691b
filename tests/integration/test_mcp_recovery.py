from __future__ import annotations

import json
from pathlib import Path

import pytest

import bmad_agent_mcp as mcp_module

# This integration test exercises the MCP-facing recovery control tools:
# - start_greenfield_workflow (creates a workflow state)
# - pause_workflow (persists a paused state)
# - recover_from_checkpoint (restore from checkpoint if available) / resume_workflow
#
# It uses a temporary project directory (tmp_path) so it does not touch the repo state.

def test_mcp_pause_and_recover(tmp_path: Path):
    # Use tmp_path as project root by passing project_path to start_greenfield_workflow
    proj = str(tmp_path)

    # Ensure docs directory exists so scan_project_structure returns sane defaults
    docs = tmp_path / "docs"
    docs.mkdir(parents=True, exist_ok=True)
    prd = docs / "prd.md"
    prd.write_text("# PRD\n\n## Epic 1: test\n\n### Story 1.1: s\n\n1. AC\n", encoding="utf-8")

    # Start a greenfield workflow via direct state_engine helper (avoids MCP wrapper)
    from workflow.state_engine import start_greenfield_workflow
    result = start_greenfield_workflow(project_path=proj, auto_confirm=False)
    # start_greenfield_workflow may return {"workflow_state":..., "report":...} or {"success": True, "execution_summary": ...}
    assert result, "start_greenfield_workflow returned empty result"
    execution_summary = result.get("execution_summary") or result.get("execution_summary") or {}
    # support older return shapes: try common keys
    state = execution_summary.get("state") or execution_summary.get("workflow_state") or result.get("workflow_state") or result.get("state") or {}
    # determine workflow id (various code paths use different keys)
    workflow_id = state.get("workflow_id") or state.get("workflow_id") or (state.get("workflow_state") or {}).get("workflow_id")
    assert workflow_id is not None

    # Pause workflow via direct workflow executor function to avoid FunctionTool wrapper
    from workflow.workflow_executor import pause_workflow_execution, recover_from_checkpoint
    pause_resp = pause_workflow_execution(project_path=proj, workflow_execution=state)
    assert isinstance(pause_resp, dict)
    assert pause_resp.get("paused") is True or pause_resp.get("success") is True

    # Attempt to list checkpoints using state_engine via MCP-level recover tool path:
    # Now try to recover from checkpoint (no checkpoint name -> uses latest) via executor
    recover_resp = recover_from_checkpoint(project_path=proj, workflow_id=workflow_id)
    # recover_from_checkpoint returns dict with restored flag or error
    assert isinstance(recover_resp, dict)
    # Either restored True or returns a clear error; assert we don't crash
    assert "restored" in recover_resp or "error" in recover_resp

    # If restored, validate returned workflow_state shape
    if recover_resp.get("restored"):
        ws = recover_resp.get("workflow_state") or {}
        assert isinstance(ws, dict)
        assert ws.get("workflow_id") is not None
