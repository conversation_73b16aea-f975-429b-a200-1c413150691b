import time
import pytest

from workflow.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>ifier, Retry<PERSON>anager, execute_with_retry
from workflow.models import RetryPolicy


class DummyErr(Exception):
    pass


def test_classify_temporary_and_permanent_errors():
    # Temporary error (by exception name)
    exc = ConnectionError("network down")
    rec = ErrorClassifier.classify_error(exc, context={}, phase="test", component="tester")
    assert rec.error_type == "temporary"
    assert rec.severity in ("low", "medium", "high")

    # Permanent error
    exc2 = FileNotFoundError("missing file")
    rec2 = ErrorClassifier.classify_error(exc2, context={}, phase="test", component="tester")
    assert rec2.error_type == "permanent"
    assert rec2.severity in ("low", "medium", "high")

    # User cancelled via context
    exc3 = DummyErr("user cancelled")
    rec3 = ErrorClassifier.classify_error(exc3, context={"user_cancelled": True}, phase="test", component="tester")
    assert rec3.error_type == "user_error"
    assert rec3.severity == "low"


def test_retry_manager_success_after_retries():
    policy = RetryPolicy(max_retries=3, base_delay=0.01, max_delay=0.05, backoff_multiplier=2.0, retry_conditions=["temporary"], timeout=5.0)
    mgr = RetryManager(policy=policy)

    calls = {"count": 0}

    def flaky():
        calls["count"] += 1
        if calls["count"] < 3:
            raise ConnectionError("temporary fail")
        return "ok"

    result = mgr.execute_with_retry(flaky, timeout=2.0, context={}, phase="test", component="tester")
    assert result == "ok"
    assert calls["count"] == 3


def test_retry_manager_raises_after_exceeding_retries():
    policy = RetryPolicy(max_retries=2, base_delay=0.01, max_delay=0.05, backoff_multiplier=2.0, retry_conditions=["temporary"], timeout=1.0)
    mgr = RetryManager(policy=policy)

    def always_fail():
        raise ConnectionError("still failing")

    with pytest.raises(Exception):
        mgr.execute_with_retry(always_fail, timeout=1.0, context={}, phase="test", component="tester")


def test_execute_with_retry_convenience_function():
    # Use the top-level helper
    policy = RetryPolicy(max_retries=2, base_delay=0.01, max_delay=0.05, backoff_multiplier=2.0, retry_conditions=["temporary"], timeout=1.0)

    calls = {"count": 0}

    def succeed_on_second():
        calls["count"] += 1
        if calls["count"] < 2:
            raise ConnectionError("temporary")
        return "done"

    res = execute_with_retry(succeed_on_second, retry_policy=policy, timeout=1.0, context={}, phase="test", component="tester")
    assert res == "done"
    assert calls["count"] == 2