"""
测试 MockFixtureGenerator 的功能
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

from workflow.mock_fixture_generator import MockFixtureGenerator, MockConfiguration, FixtureConfiguration


class TestMockFixtureGenerator:
    """测试 MockFixtureGenerator 类"""

    @pytest.fixture
    def temp_project_dir(self):
        """创建临时项目目录"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def mock_generator(self, temp_project_dir):
        """创建 MockFixtureGenerator 实例"""
        return MockFixtureGenerator(temp_project_dir)

    @pytest.fixture
    def sample_python_file(self, temp_project_dir):
        """创建示例 Python 文件"""
        file_path = Path(temp_project_dir) / "sample.py"
        content = '''
import os
import requests
import sqlite3
from pathlib import Path
from datetime import datetime

def process_file(file_path):
    """处理文件的函数"""
    if os.path.exists(file_path):
        with open(file_path, 'r') as f:
            content = f.read()
        return content
    return None

def fetch_data(url):
    """获取网络数据"""
    response = requests.get(url)
    return response.json()

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.connection = sqlite3.connect(db_path)
    
    def execute_query(self, query):
        cursor = self.connection.cursor()
        cursor.execute(query)
        return cursor.fetchall()
'''
        with open(file_path, 'w') as f:
            f.write(content)
        return str(file_path.relative_to(temp_project_dir))

    def test_initialization(self, mock_generator):
        """测试初始化"""
        assert mock_generator is not None
        assert mock_generator.project_root is not None
        assert len(mock_generator.external_dependencies) > 0

    def test_analyze_dependencies(self, mock_generator, sample_python_file):
        """测试依赖分析"""
        dependencies = mock_generator.analyze_dependencies([sample_python_file])
        
        assert sample_python_file in dependencies
        file_deps = dependencies[sample_python_file]
        
        # 检查是否识别了主要依赖
        assert 'os' in file_deps
        assert 'requests' in file_deps
        assert 'sqlite3' in file_deps
        assert 'pathlib' in file_deps
        assert 'datetime' in file_deps

    def test_generate_mock_configurations(self, mock_generator, sample_python_file):
        """测试 Mock 配置生成"""
        dependencies = mock_generator.analyze_dependencies([sample_python_file])
        mock_configs = mock_generator.generate_mock_configurations(dependencies)
        
        assert sample_python_file in mock_configs
        file_mocks = mock_configs[sample_python_file]
        
        # 检查是否生成了 Mock 配置
        assert len(file_mocks) > 0
        
        # 检查 Mock 配置的结构
        for mock_config in file_mocks:
            assert isinstance(mock_config, MockConfiguration)
            assert mock_config.target is not None
            assert mock_config.mock_type is not None

    def test_generate_fixture_configurations(self, mock_generator, sample_python_file):
        """测试 Fixture 配置生成"""
        dependencies = mock_generator.analyze_dependencies([sample_python_file])
        mock_configs = mock_generator.generate_mock_configurations(dependencies)
        fixture_configs = mock_generator.generate_fixture_configurations([sample_python_file], mock_configs)
        
        assert sample_python_file in fixture_configs
        file_fixtures = fixture_configs[sample_python_file]
        
        # 检查是否生成了 Fixture 配置
        assert len(file_fixtures) > 0
        
        # 检查 Fixture 配置的结构
        for fixture_config in file_fixtures:
            assert isinstance(fixture_config, FixtureConfiguration)
            assert fixture_config.name is not None
            assert fixture_config.scope is not None
            assert fixture_config.data_type is not None

    def test_generate_mock_code(self, mock_generator):
        """测试 Mock 代码生成"""
        mock_configs = [
            MockConfiguration(
                target="requests.get",
                mock_type="function",
                return_value="Mock()",
                side_effect=None,
                attributes={},
                call_count=None,
                call_args=[]
            )
        ]
        
        mock_code = mock_generator.generate_mock_code(mock_configs)
        
        assert "from unittest.mock import Mock, patch, MagicMock" in mock_code
        assert "@patch('requests.get')" in mock_code
        assert "def mock_requests_get" in mock_code

    def test_generate_fixture_code(self, mock_generator):
        """测试 Fixture 代码生成"""
        fixture_configs = [
            FixtureConfiguration(
                name="temp_dir",
                scope="function",
                params=None,
                setup_code="temp_dir = tempfile.mkdtemp()\nyield temp_dir\nshutil.rmtree(temp_dir)",
                teardown_code="",
                dependencies=[],
                data_type="filesystem"
            )
        ]
        
        fixture_code = mock_generator.generate_fixture_code(fixture_configs)
        
        assert "import pytest" in fixture_code
        assert "@pytest.fixture" in fixture_code
        assert "def temp_dir" in fixture_code

    def test_generate_conftest_py(self, mock_generator):
        """测试 conftest.py 生成"""
        fixture_configs = [
            FixtureConfiguration(
                name="temp_dir",
                scope="function",
                params=None,
                setup_code="temp_dir = tempfile.mkdtemp()\nyield temp_dir\nshutil.rmtree(temp_dir)",
                teardown_code="",
                dependencies=[],
                data_type="filesystem"
            )
        ]
        
        mock_configs = [
            MockConfiguration(
                target="requests.get",
                mock_type="function",
                return_value="Mock()",
                side_effect=None,
                attributes={},
                call_count=None,
                call_args=[]
            )
        ]
        
        conftest_content = mock_generator.generate_conftest_py(fixture_configs, mock_configs)
        
        assert "# Auto-generated conftest.py" in conftest_content
        assert "import pytest" in conftest_content
        assert "def temp_dir" in conftest_content
        assert "def mock_requests_get" in conftest_content

    def test_external_dependency_detection(self, mock_generator):
        """测试外部依赖检测"""
        # 测试已知的外部依赖
        assert mock_generator._is_external_dependency("requests")
        assert mock_generator._is_external_dependency("numpy.array")
        
        # 测试内部依赖
        assert not mock_generator._is_external_dependency("workflow.models")
        assert not mock_generator._is_external_dependency("tests.unit")

    def test_mock_strategy_creation(self, mock_generator):
        """测试 Mock 策略创建"""
        # 测试数据库连接策略
        db_config = {'type': 'database', 'mock_strategy': 'connection'}
        mock_config = mock_generator._create_mock_by_strategy("sqlite3", db_config)
        
        assert mock_config.mock_type == 'class'
        assert 'cursor.return_value' in mock_config.attributes
        
        # 测试 HTTP 客户端策略
        http_config = {'type': 'network', 'mock_strategy': 'http_client'}
        mock_config = mock_generator._create_mock_by_strategy("requests", http_config)
        
        assert mock_config.mock_type == 'module'
        assert 'get.return_value.status_code' in mock_config.attributes

    def test_basic_fixtures_generation(self, mock_generator):
        """测试基础 fixture 生成"""
        fixtures = mock_generator._generate_basic_fixtures("sample.py")
        
        assert len(fixtures) >= 2
        fixture_names = [f.name for f in fixtures]
        assert "temp_dir" in fixture_names
        assert "test_data_dir" in fixture_names

    def test_empty_file_handling(self, mock_generator, temp_project_dir):
        """测试空文件处理"""
        empty_file = Path(temp_project_dir) / "empty.py"
        empty_file.write_text("")
        
        dependencies = mock_generator.analyze_dependencies(["empty.py"])
        assert "empty.py" in dependencies
        assert len(dependencies["empty.py"]) == 0

    def test_nonexistent_file_handling(self, mock_generator):
        """测试不存在文件的处理"""
        dependencies = mock_generator.analyze_dependencies(["nonexistent.py"])
        # 应该跳过不存在的文件
        assert len(dependencies) == 0 or dependencies.get("nonexistent.py", []) == []
