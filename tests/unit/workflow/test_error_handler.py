from __future__ import annotations

import time
import types
import pytest

from workflow.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RetryManager, generate_recovery_suggestions
from workflow.models import RetryPolicy


def test_error_classifier_temporary():
    err = ErrorClassifier.classify_error(ConnectionError("net down"), context={"op": "x"}, phase="p", component="c")
    assert err.error_type == "temporary"
    assert err.severity in {"low", "medium", "high", "critical"}


def test_error_classifier_permanent():
    err = ErrorClassifier.classify_error(FileNotFoundError("no file"), context={"file_path": "abc"}, phase="p", component="c")
    assert err.error_type == "permanent"


def test_error_classifier_user_error_from_context():
    err = ErrorClassifier.classify_error(RuntimeError("user aborted"), context={"user_cancelled": True}, phase="p", component="c")
    assert err.error_type == "user_error"
    assert err.severity == "low"


def test_generate_recovery_suggestions_basic_rules():
    e = ErrorClassifier.classify_error(ConnectionError("conn timeout"), context={"path": "/tmp/x"}, phase="document_processing", component="document_processor")
    sugg = generate_recovery_suggestions(e)
    assert any("重试" in s or "重试操作" in s for s in sugg)
    assert any("网络" in s for s in sugg)
    assert any("/tmp/x" in s for s in sugg)


def test_retry_manager_success_after_transient_failures(monkeypatch: pytest.MonkeyPatch):
    calls = {"n": 0}

    def flaky():
        if calls["n"] < 2:
            calls["n"] += 1
            raise ConnectionError("boom")
        return "ok"

    policy = RetryPolicy(max_retries=5, base_delay=0.01, max_delay=0.02, backoff_multiplier=2.0, retry_conditions=["temporary"], timeout=0.5)
    mgr = RetryManager(policy=policy)
    out = mgr.execute_with_retry(flaky, context={"case": "flaky"}, phase="unit_test", component="test_component")
    assert out == "ok"
    assert calls["n"] == 2


def test_retry_manager_timeout(monkeypatch: pytest.MonkeyPatch):
    def always_fail():
        raise ConnectionError("still failing")

    policy = RetryPolicy(max_retries=100, base_delay=0.01, max_delay=0.02, backoff_multiplier=2.0, retry_conditions=["temporary"], timeout=0.05)
    mgr = RetryManager(policy=policy)
    t0 = time.time()
    with pytest.raises(ConnectionError):
        mgr.execute_with_retry(always_fail, context={"case": "timeout"}, phase="unit_test", component="test_component")
    assert time.time() - t0 < 1.0  # should not hang



def test_generate_recovery_suggestions_permission_and_timeout():
    e_perm = ErrorClassifier.classify_error(PermissionError("permission denied"), context={"file_path": "/etc/secret"}, phase="document_processing", component="document_processor")
    sugg_perm = generate_recovery_suggestions(e_perm)
    assert any("权限" in s or "权限" in s for s in sugg_perm)
    assert any("/etc/secret" in s for s in sugg_perm)

    e_net = ErrorClassifier.classify_error(ConnectionError("connection timeout"), context={}, phase="network", component="fetcher")
    sugg_net = generate_recovery_suggestions(e_net)
    assert any("网络" in s or "连接" in s or "超时" in s for s in sugg_net)

def test_retry_policy_compute_delay():
    from workflow.models import RetryPolicy
    p = RetryPolicy(max_retries=5, base_delay=0.5, max_delay=2.0, backoff_multiplier=2.0)
    # retry 0 -> base_delay
    assert p.compute_delay(0) == 0.5
    # retry 1 -> base * multiplier
    assert p.compute_delay(1) == 1.0
    # retry 2 -> 2.0 but capped by max_delay
    assert p.compute_delay(2) == 2.0
    # retry 3 -> would be 4.0 but capped to max_delay
    assert p.compute_delay(3) == 2.0

def test_retry_manager_should_retry_and_limits():
    from workflow.models import RetryPolicy
    from workflow.error_handler import RetryManager, ErrorClassifier
    policy = RetryPolicy(max_retries=2, base_delay=0.01, max_delay=0.02, backoff_multiplier=2.0, retry_conditions=["temporary"])
    mgr = RetryManager(policy=policy)

    # prepare a temporary ErrorRecord and simulate retry_count
    exc = ConnectionError("net")
    rec = ErrorClassifier.classify_error(exc, context={}, phase="test", component="c")
    rec.retry_count = 0
    assert mgr.should_retry(rec) is True

    rec.retry_count = 2
    # equal to max_retries -> should not retry
    assert mgr.should_retry(rec) is False

    # non-temporary error should not be retried
    rec2 = ErrorClassifier.classify_error(FileNotFoundError("missing"), context={}, phase="test", component="c")
    rec2.retry_count = 0
    assert mgr.should_retry(rec2) is False

def test_execute_with_retry_respects_max_retries(monkeypatch):
    from workflow.models import RetryPolicy
    from workflow.error_handler import RetryManager

    calls = {"n": 0}

    def always_fail():
        calls["n"] += 1
        raise ConnectionError("boom")

    # Use a policy with small max_retries
    policy = RetryPolicy(max_retries=3, base_delay=0.01, max_delay=0.02, backoff_multiplier=2.0, retry_conditions=["temporary"], timeout=1.0)
    mgr = RetryManager(policy=policy)

    # monkeypatch sleep to avoid slowing tests
    import time as _time
    monkeypatch.setattr(_time, "sleep", lambda s: None)

    with pytest.raises(ConnectionError):
        mgr.execute_with_retry(always_fail, context={}, phase="unit", component="t")

    # calls should be max_retries + 1 attempts (initial + retries)
    assert calls["n"] == policy.max_retries + 1
