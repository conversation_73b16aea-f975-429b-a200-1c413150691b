"""
Unit test template for Python modules
Uses pytest framework with AAA pattern (<PERSON><PERSON><PERSON>, Act, Assert)
"""

import pytest
from unittest.mock import Mock, patch
# Import the module under test; adjust if module path differs
from sample_module import Calculator, add_numbers, divide_numbers


def test_calculator_multiply_basic():
    """Test Calculator.multiply - basic"""
    # Arrange
    instance = Calculator()
    a = 2
    b = 3

    # Act
    result = instance.multiply(a, b)

    # Assert
    assert result == 6


def test_calculator_get_precision_basic():
    """Test Calculator.get_precision - basic"""
    # Arrange
    instance = Calculator()

    # Act
    result = instance.get_precision()

    # Assert
    assert isinstance(result, int) or isinstance(result, float)


def test_add_numbers_basic():
    """Test add_numbers - basic"""
    # Arrange
    a = 4
    b = 5

    # Act
    result = add_numbers(a, b)

    # Assert
    assert result == 9


def test_divide_numbers_basic():
    """Test divide_numbers - basic"""
    # Arrange
    a = 10
    b = 2

    # Act
    result = divide_numbers(a, b)

    # Assert
    assert result == 5
