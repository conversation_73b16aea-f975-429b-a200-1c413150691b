# Auto-generated conftest.py
# This file contains shared fixtures and mock configurations

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

import pytest

@pytest.fixture
def temp_dir():
    """Auto-generated fixture for filesystem testing"""
    import tempfile
    import shutil
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)

@pytest.fixture(scope='session')
def test_data_dir():
    """Auto-generated fixture for filesystem testing"""
    from pathlib import Path
    test_data_dir = Path(__file__).parent / "fixtures" / "data"
    test_data_dir.mkdir(parents=True, exist_ok=True)
    return test_data_dir

@pytest.fixture(scope='function', params=[{'id': 1, 'name': 'test_item_1', 'value': 100}, {'id': 2, 'name': 'test_item_2', 'value': 200}, {'id': 3, 'name': 'test_item_3', 'value': 300}])
def sample_data(request):
    """Auto-generated fixture for object testing"""
    return request.param

@pytest.fixture(scope='session')
def json_test_data():
    """Auto-generated fixture for object testing"""
    import json
    test_data = {
        "users": [
            {"id": 1, "name": "Alice", "email": "<EMAIL>"},
            {"id": 2, "name": "Bob", "email": "<EMAIL>"}
        ],
        "settings": {
            "debug": True,
            "max_connections": 100
        }
    }
    return test_data


# Mock fixtures

@pytest.fixture
def mock_mock_setups():
    with patch('mock_setups') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

@pytest.fixture
def mock_logging():
    with patch('logging') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

@pytest.fixture
def mock_f():
    with patch('f') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

@pytest.fixture
def mock_fixture_codes():
    with patch('fixture_codes') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

@pytest.fixture
def mock_mock_imports():
    with patch('mock_imports') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

@pytest.fixture
def mock_self():
    with patch('self') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

@pytest.fixture
def mock_line():
    with patch('line') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

@pytest.fixture
def mock_pathlib_Path():
    with patch('pathlib.Path') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

@pytest.fixture
def mock_fixtures():
    with patch('fixtures') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

@pytest.fixture
def mock_fixture_imports():
    with patch('fixture_imports') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

@pytest.fixture
def mock_dependencies():
    with patch('dependencies') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

@pytest.fixture
def mock_file_path():
    with patch('file_path') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

@pytest.fixture
def mock_mocks():
    with patch('mocks') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

@pytest.fixture
def mock_dependency():
    with patch('dependency') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

@pytest.fixture
def mock_ast():
    with patch('ast') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

@pytest.fixture
def mock_logger():
    with patch('logger') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

@pytest.fixture
def mock_pathlib():
    with patch('pathlib') as mock_obj:
        # Configure mock attributes
        for attr, value in {}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj

