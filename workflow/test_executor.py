from dataclasses import dataclass
import subprocess
import json
import os
import tempfile
import xml.etree.ElementTree as ET
import re
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


@dataclass
class TestFailure:
    """测试失败详情"""
    test_name: str
    failure_type: str
    failure_message: str
    traceback: str
    file_path: str
    line_number: int


@dataclass
class TestExecutionResult:
    """增强的测试执行结果"""
    success: bool
    return_code: int
    stdout: str
    stderr: str
    junit_report: Dict[str, Any]
    # 新增字段
    total_tests: int
    passed_tests: int
    failed_tests: int
    skipped_tests: int
    execution_time: float
    failures: List[TestFailure]
    coverage_percentage: Optional[float]
    detailed_results: Dict[str, Any]


class TestExecutor:
    """
    增强的测试执行引擎：管理测试执行、结果收集和分析
    支持详细的失败诊断、覆盖率分析和测试报告生成
    """

    def __init__(self, pytest_path: str = "python3", project_root: Optional[str] = None):
        self.pytest_path = pytest_path
        self.project_root = Path(project_root) if project_root else Path.cwd()
        logger.info(f"TestExecutor initialized with project_root: {self.project_root}")

    def run_tests(self, test_paths: List[str], extra_args: List[str] = None, cwd: str = None,
                  with_coverage: bool = False) -> TestExecutionResult:
        """
        运行测试并收集详细结果

        Args:
            test_paths: 测试文件路径列表
            extra_args: 额外的 pytest 参数
            cwd: 工作目录
            with_coverage: 是否启用覆盖率收集

        Returns:
            TestExecutionResult: 详细的测试执行结果
        """
        start_time = time.time()

        # 构建 pytest 命令
        args = [self.pytest_path, "-m", "pytest", "-v", "--tb=short"]
        if extra_args:
            args.extend(extra_args)

        # 设置 JUnit XML 报告
        report_file = tempfile.NamedTemporaryFile(prefix="pytest-junit-", suffix=".xml", delete=False)
        report_path = report_file.name
        report_file.close()
        args.extend(["--junit-xml", report_path])

        # 如果启用覆盖率
        coverage_file = None
        if with_coverage:
            coverage_file = tempfile.NamedTemporaryFile(prefix="coverage-", suffix=".xml", delete=False)
            coverage_path = coverage_file.name
            coverage_file.close()
            args.extend(["--cov", str(self.project_root), "--cov-report", f"xml:{coverage_path}"])

        args.extend(test_paths)

        logger.info(f"Running pytest: {' '.join(args)}")

        # 执行测试
        try:
            proc = subprocess.run(
                args,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=cwd or str(self.project_root),
                timeout=300,  # 5分钟超时
                text=True
            )

            execution_time = time.time() - start_time

            # 解析结果
            result = self._parse_test_results(
                proc.returncode,
                proc.stdout,
                proc.stderr,
                report_path,
                coverage_path if with_coverage else None,
                execution_time
            )

            return result

        except subprocess.TimeoutExpired:
            logger.error("Test execution timed out")
            return self._create_error_result("Test execution timed out", time.time() - start_time)
        except Exception as e:
            logger.error(f"Test execution failed: {e}")
            return self._create_error_result(str(e), time.time() - start_time)
        finally:
            # 清理临时文件
            self._cleanup_temp_files([report_path] + ([coverage_path] if with_coverage and coverage_file else []))

    def _parse_test_results(self, return_code: int, stdout: str, stderr: str,
                           junit_path: str, coverage_path: Optional[str],
                           execution_time: float) -> TestExecutionResult:
        """解析测试结果"""

        # 解析 JUnit XML 报告
        junit_data = self._parse_junit_xml(junit_path)

        # 解析覆盖率报告
        coverage_percentage = None
        if coverage_path:
            coverage_percentage = self._parse_coverage_xml(coverage_path)

        # 从 stdout 解析基本统计信息
        test_stats = self._parse_pytest_output(stdout)

        # 解析失败详情
        failures = self._parse_test_failures(stdout, stderr, junit_data)

        return TestExecutionResult(
            success=return_code == 0,
            return_code=return_code,
            stdout=stdout,
            stderr=stderr,
            junit_report=junit_data,
            total_tests=test_stats.get('total', 0),
            passed_tests=test_stats.get('passed', 0),
            failed_tests=test_stats.get('failed', 0),
            skipped_tests=test_stats.get('skipped', 0),
            execution_time=execution_time,
            failures=failures,
            coverage_percentage=coverage_percentage,
            detailed_results=test_stats
        )

    def _parse_junit_xml(self, junit_path: str) -> Dict[str, Any]:
        """解析 JUnit XML 报告"""
        junit_data = {}

        try:
            if os.path.exists(junit_path):
                with open(junit_path, 'r', encoding='utf-8') as f:
                    xml_content = f.read()
                    junit_data['xml'] = xml_content

                # 解析 XML 结构
                root = ET.fromstring(xml_content)
                junit_data['testsuites'] = []

                for testsuite in root.findall('.//testsuite'):
                    suite_data = {
                        'name': testsuite.get('name', ''),
                        'tests': int(testsuite.get('tests', 0)),
                        'failures': int(testsuite.get('failures', 0)),
                        'errors': int(testsuite.get('errors', 0)),
                        'skipped': int(testsuite.get('skipped', 0)),
                        'time': float(testsuite.get('time', 0)),
                        'testcases': []
                    }

                    for testcase in testsuite.findall('testcase'):
                        case_data = {
                            'name': testcase.get('name', ''),
                            'classname': testcase.get('classname', ''),
                            'time': float(testcase.get('time', 0)),
                            'status': 'passed'
                        }

                        # 检查失败或错误
                        failure = testcase.find('failure')
                        error = testcase.find('error')
                        skipped = testcase.find('skipped')

                        if failure is not None:
                            case_data['status'] = 'failed'
                            case_data['failure'] = {
                                'type': failure.get('type', ''),
                                'message': failure.get('message', ''),
                                'text': failure.text or ''
                            }
                        elif error is not None:
                            case_data['status'] = 'error'
                            case_data['error'] = {
                                'type': error.get('type', ''),
                                'message': error.get('message', ''),
                                'text': error.text or ''
                            }
                        elif skipped is not None:
                            case_data['status'] = 'skipped'
                            case_data['skipped'] = {
                                'type': skipped.get('type', ''),
                                'message': skipped.get('message', ''),
                                'text': skipped.text or ''
                            }

                        suite_data['testcases'].append(case_data)

                    junit_data['testsuites'].append(suite_data)

        except Exception as e:
            logger.warning(f"Failed to parse JUnit XML: {e}")

        return junit_data

    def _parse_coverage_xml(self, coverage_path: str) -> Optional[float]:
        """解析覆盖率 XML 报告"""
        try:
            if os.path.exists(coverage_path):
                root = ET.parse(coverage_path).getroot()
                coverage_elem = root.find('.//coverage')
                if coverage_elem is not None:
                    line_rate = coverage_elem.get('line-rate')
                    if line_rate:
                        return float(line_rate) * 100  # 转换为百分比
        except Exception as e:
            logger.warning(f"Failed to parse coverage XML: {e}")

        return None

    def _parse_pytest_output(self, stdout: str) -> Dict[str, Any]:
        """解析 pytest 输出，提取测试统计信息"""
        stats = {
            'total': 0,
            'passed': 0,
            'failed': 0,
            'skipped': 0,
            'errors': 0
        }

        try:
            # 查找测试结果摘要行
            lines = stdout.splitlines()
            for line in lines:
                line = line.strip()

                # 匹配类似 "5 passed, 2 failed, 1 skipped in 0.12s" 的行
                if ' passed' in line or ' failed' in line or ' skipped' in line:
                    # 提取数字
                    passed_match = re.search(r'(\d+) passed', line)
                    failed_match = re.search(r'(\d+) failed', line)
                    skipped_match = re.search(r'(\d+) skipped', line)
                    error_match = re.search(r'(\d+) error', line)

                    if passed_match:
                        stats['passed'] = int(passed_match.group(1))
                    if failed_match:
                        stats['failed'] = int(failed_match.group(1))
                    if skipped_match:
                        stats['skipped'] = int(skipped_match.group(1))
                    if error_match:
                        stats['errors'] = int(error_match.group(1))

                    stats['total'] = stats['passed'] + stats['failed'] + stats['skipped'] + stats['errors']
                    break

        except Exception as e:
            logger.warning(f"Failed to parse pytest output: {e}")

        return stats

    def _parse_test_failures(self, stdout: str, stderr: str, junit_data: Dict[str, Any]) -> List[TestFailure]:
        """解析测试失败详情"""
        failures = []

        try:
            # 从 JUnit 数据中提取失败信息
            for testsuite in junit_data.get('testsuites', []):
                for testcase in testsuite.get('testcases', []):
                    if testcase.get('status') in ['failed', 'error']:
                        failure_info = testcase.get('failure') or testcase.get('error', {})

                        # 尝试从 classname 中提取文件路径和行号
                        classname = testcase.get('classname', '')
                        file_path = ''
                        line_number = 0

                        if '::' in classname:
                            parts = classname.split('::')
                            if parts:
                                file_path = parts[0].replace('.', '/') + '.py'

                        # 从 traceback 中提取行号
                        traceback_text = failure_info.get('text', '')
                        line_match = re.search(r':(\d+):', traceback_text)
                        if line_match:
                            line_number = int(line_match.group(1))

                        failure = TestFailure(
                            test_name=testcase.get('name', ''),
                            failure_type=failure_info.get('type', 'Unknown'),
                            failure_message=failure_info.get('message', ''),
                            traceback=traceback_text,
                            file_path=file_path,
                            line_number=line_number
                        )
                        failures.append(failure)

        except Exception as e:
            logger.warning(f"Failed to parse test failures: {e}")

        return failures

    def _create_error_result(self, error_message: str, execution_time: float) -> TestExecutionResult:
        """创建错误结果"""
        return TestExecutionResult(
            success=False,
            return_code=-1,
            stdout="",
            stderr=error_message,
            junit_report={},
            total_tests=0,
            passed_tests=0,
            failed_tests=0,
            skipped_tests=0,
            execution_time=execution_time,
            failures=[],
            coverage_percentage=None,
            detailed_results={'error': error_message}
        )

    def _cleanup_temp_files(self, file_paths: List[str]) -> None:
        """清理临时文件"""
        for file_path in file_paths:
            try:
                if file_path and os.path.exists(file_path):
                    os.unlink(file_path)
            except Exception as e:
                logger.debug(f"Failed to cleanup temp file {file_path}: {e}")

    def run_specific_tests(self, test_patterns: List[str], **kwargs) -> TestExecutionResult:
        """运行特定的测试模式"""
        return self.run_tests(test_patterns, **kwargs)

    def run_failed_tests_only(self, previous_result: TestExecutionResult, **kwargs) -> TestExecutionResult:
        """只运行之前失败的测试"""
        if not previous_result.failures:
            logger.info("No failed tests to re-run")
            return self._create_error_result("No failed tests to re-run", 0.0)

        # 构建失败测试的模式
        failed_patterns = []
        for failure in previous_result.failures:
            if failure.file_path and failure.test_name:
                pattern = f"{failure.file_path}::{failure.test_name}"
                failed_patterns.append(pattern)

        if not failed_patterns:
            return self._create_error_result("Could not determine failed test patterns", 0.0)

        logger.info(f"Re-running {len(failed_patterns)} failed tests")
        return self.run_tests(failed_patterns, **kwargs)

    def generate_test_report(self, result: TestExecutionResult) -> str:
        """生成详细的测试报告"""
        report_lines = []

        # 标题
        report_lines.append("=" * 60)
        report_lines.append("测试执行报告")
        report_lines.append("=" * 60)

        # 基本统计
        report_lines.append(f"执行时间: {result.execution_time:.2f}秒")
        report_lines.append(f"总测试数: {result.total_tests}")
        report_lines.append(f"通过: {result.passed_tests}")
        report_lines.append(f"失败: {result.failed_tests}")
        report_lines.append(f"跳过: {result.skipped_tests}")
        report_lines.append(f"成功率: {(result.passed_tests / max(result.total_tests, 1)) * 100:.1f}%")

        if result.coverage_percentage is not None:
            report_lines.append(f"代码覆盖率: {result.coverage_percentage:.1f}%")

        # 失败详情
        if result.failures:
            report_lines.append("\n" + "=" * 40)
            report_lines.append("失败测试详情")
            report_lines.append("=" * 40)

            for i, failure in enumerate(result.failures, 1):
                report_lines.append(f"\n{i}. {failure.test_name}")
                report_lines.append(f"   类型: {failure.failure_type}")
                report_lines.append(f"   文件: {failure.file_path}:{failure.line_number}")
                report_lines.append(f"   消息: {failure.failure_message}")

                if failure.traceback:
                    # 只显示关键的 traceback 行
                    tb_lines = failure.traceback.splitlines()
                    relevant_lines = [line for line in tb_lines if 'assert' in line.lower() or 'error' in line.lower()]
                    if relevant_lines:
                        report_lines.append("   关键错误:")
                        for line in relevant_lines[:3]:  # 最多显示3行
                            report_lines.append(f"     {line.strip()}")

        # 性能分析
        if result.execution_time > 10:  # 如果执行时间超过10秒
            report_lines.append("\n" + "=" * 40)
            report_lines.append("性能提醒")
            report_lines.append("=" * 40)
            report_lines.append(f"测试执行时间较长 ({result.execution_time:.2f}秒)")
            report_lines.append("建议检查是否有慢速测试或可以并行化的测试")

        return "\n".join(report_lines)

    def diagnose_failures(self, result: TestExecutionResult) -> List[str]:
        """诊断测试失败并提供建议"""
        suggestions = []

        if not result.failures:
            return suggestions

        # 分析失败类型
        failure_types = {}
        for failure in result.failures:
            failure_type = failure.failure_type
            if failure_type not in failure_types:
                failure_types[failure_type] = []
            failure_types[failure_type].append(failure)

        # 根据失败类型提供建议
        for failure_type, failures in failure_types.items():
            count = len(failures)

            if 'AssertionError' in failure_type:
                suggestions.append(f"发现 {count} 个断言错误，建议检查测试期望值是否正确")
            elif 'ImportError' in failure_type or 'ModuleNotFoundError' in failure_type:
                suggestions.append(f"发现 {count} 个导入错误，建议检查模块路径和依赖")
            elif 'AttributeError' in failure_type:
                suggestions.append(f"发现 {count} 个属性错误，建议检查对象接口是否正确")
            elif 'TypeError' in failure_type:
                suggestions.append(f"发现 {count} 个类型错误，建议检查参数类型和函数签名")
            elif 'ValueError' in failure_type:
                suggestions.append(f"发现 {count} 个值错误，建议检查输入参数的有效性")

        # 分析失败模式
        file_failures = {}
        for failure in result.failures:
            file_path = failure.file_path
            if file_path not in file_failures:
                file_failures[file_path] = []
            file_failures[file_path].append(failure)

        # 如果某个文件有多个失败
        for file_path, failures in file_failures.items():
            if len(failures) > 1:
                suggestions.append(f"文件 {file_path} 有 {len(failures)} 个失败测试，建议重点检查该文件")

        return suggestions

    def run_tests_with_coverage(self, test_paths: List[str], cov_report_path: str = None,
                               extra_args: List[str] = None, cwd: str = None) -> TestExecutionResult:
        """
        运行带覆盖率的测试（兼容性方法）

        Args:
            test_paths: 测试文件路径列表
            cov_report_path: 覆盖率报告输出路径（可选）
            extra_args: 额外的 pytest 参数
            cwd: 工作目录

        Returns:
            TestExecutionResult: 测试执行结果
        """
        # 使用新的 run_tests 方法，启用覆盖率
        result = self.run_tests(test_paths, extra_args, cwd, with_coverage=True)

        # 如果指定了覆盖率报告路径，尝试复制文件
        if cov_report_path and result.coverage_percentage is not None:
            try:
                # 查找生成的覆盖率文件并复制到指定位置
                coverage_files = ['coverage.xml', 'htmlcov/index.html']
                for coverage_file in coverage_files:
                    source_path = os.path.join(cwd or str(self.project_root), coverage_file)
                    if os.path.exists(source_path):
                        import shutil
                        shutil.copy2(source_path, cov_report_path)
                        logger.info(f"Coverage report copied to {cov_report_path}")
                        break
            except Exception as e:
                logger.warning(f"Failed to copy coverage report: {e}")

        return result