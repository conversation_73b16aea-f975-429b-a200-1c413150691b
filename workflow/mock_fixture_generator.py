"""
Mock 和 Fixture 生成器

此模块实现自动生成测试所需的 Mock 对象和 Fixture，包括：
- 自动识别外部依赖
- 生成 Mock 对象配置
- 创建测试数据 Fixture
- 支持数据库、文件系统、网络调用的模拟
"""

import ast
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class MockConfiguration:
    """Mock 配置"""
    target: str                    # Mock 目标（模块.类.方法）
    mock_type: str                # Mock 类型（function, class, module）
    return_value: Any             # 返回值
    side_effect: Optional[str]    # 副作用
    attributes: Dict[str, Any]    # 属性配置
    call_count: Optional[int]     # 期望调用次数
    call_args: List[Any]          # 期望调用参数


@dataclass
class FixtureConfiguration:
    """Fixture 配置"""
    name: str                     # Fixture 名称
    scope: str                    # 作用域（function, class, module, session）
    params: Optional[List[Any]]   # 参数化数据
    setup_code: str              # 设置代码
    teardown_code: str           # 清理代码
    dependencies: List[str]       # 依赖的其他 fixture
    data_type: str               # 数据类型（database, file, network, object）


class MockFixtureGenerator:
    """Mock 和 Fixture 生成器"""
    
    def __init__(self, project_root: str):
        """
        Args:
            project_root: 项目根目录
        """
        self.project_root = Path(project_root)
        
        # 常见的外部依赖模式
        self.external_dependencies = {
            # 数据库相关
            'sqlite3': {'type': 'database', 'mock_strategy': 'connection'},
            'psycopg2': {'type': 'database', 'mock_strategy': 'connection'},
            'pymongo': {'type': 'database', 'mock_strategy': 'client'},
            'sqlalchemy': {'type': 'database', 'mock_strategy': 'engine'},
            
            # 文件系统
            'pathlib.Path': {'type': 'filesystem', 'mock_strategy': 'path_operations'},
            'os.path': {'type': 'filesystem', 'mock_strategy': 'path_functions'},
            'shutil': {'type': 'filesystem', 'mock_strategy': 'file_operations'},
            'tempfile': {'type': 'filesystem', 'mock_strategy': 'temp_files'},
            
            # 网络请求
            'requests': {'type': 'network', 'mock_strategy': 'http_client'},
            'urllib': {'type': 'network', 'mock_strategy': 'url_operations'},
            'httpx': {'type': 'network', 'mock_strategy': 'async_http'},
            'aiohttp': {'type': 'network', 'mock_strategy': 'async_http'},
            
            # 时间相关
            'datetime': {'type': 'time', 'mock_strategy': 'datetime_operations'},
            'time': {'type': 'time', 'mock_strategy': 'time_functions'},
            
            # 系统相关
            'subprocess': {'type': 'system', 'mock_strategy': 'process_execution'},
            'os': {'type': 'system', 'mock_strategy': 'os_operations'},
            'sys': {'type': 'system', 'mock_strategy': 'system_info'},
            
            # 日志
            'logging': {'type': 'logging', 'mock_strategy': 'logger_operations'}
        }
        
        logger.info(f"MockFixtureGenerator initialized with project_root: {self.project_root}")
    
    def analyze_dependencies(self, source_files: List[str]) -> Dict[str, List[str]]:
        """
        分析源代码文件的外部依赖
        
        Args:
            source_files: 源代码文件列表
            
        Returns:
            Dict[str, List[str]]: 文件到依赖列表的映射
        """
        file_dependencies = {}
        
        for source_file in source_files:
            try:
                file_path = self.project_root / source_file
                if not file_path.exists():
                    continue
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                dependencies = self._extract_dependencies_from_ast(content)
                file_dependencies[source_file] = dependencies
                
            except Exception as e:
                logger.warning(f"Failed to analyze dependencies in {source_file}: {e}")
                file_dependencies[source_file] = []
        
        return file_dependencies
    
    def _extract_dependencies_from_ast(self, content: str) -> List[str]:
        """从 AST 中提取依赖"""
        dependencies = set()
        
        try:
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                # 处理 import 语句
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        dependencies.add(alias.name)
                
                # 处理 from import 语句
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        dependencies.add(node.module)
                        # 也添加具体的导入项
                        for alias in node.names:
                            full_name = f"{node.module}.{alias.name}"
                            dependencies.add(full_name)
                
                # 处理函数调用中的模块使用
                elif isinstance(node, ast.Call):
                    if isinstance(node.func, ast.Attribute):
                        # 处理 module.function() 调用
                        if isinstance(node.func.value, ast.Name):
                            module_name = node.func.value.id
                            dependencies.add(module_name)
        
        except Exception as e:
            logger.debug(f"Failed to parse AST: {e}")
        
        return list(dependencies)
    
    def generate_mock_configurations(self, dependencies: Dict[str, List[str]]) -> Dict[str, List[MockConfiguration]]:
        """
        为依赖生成 Mock 配置
        
        Args:
            dependencies: 文件依赖映射
            
        Returns:
            Dict[str, List[MockConfiguration]]: 文件到 Mock 配置列表的映射
        """
        file_mocks = {}
        
        for source_file, deps in dependencies.items():
            mocks = []
            
            for dep in deps:
                # 检查是否是需要 Mock 的外部依赖
                mock_config = self._create_mock_for_dependency(dep)
                if mock_config:
                    mocks.append(mock_config)
            
            file_mocks[source_file] = mocks
        
        return file_mocks
    
    def _create_mock_for_dependency(self, dependency: str) -> Optional[MockConfiguration]:
        """为特定依赖创建 Mock 配置"""
        
        # 检查是否匹配已知的外部依赖
        for pattern, config in self.external_dependencies.items():
            if dependency.startswith(pattern) or pattern in dependency:
                return self._create_mock_by_strategy(dependency, config)
        
        # 如果不是已知的外部依赖，检查是否是标准库或第三方库
        if self._is_external_dependency(dependency):
            return MockConfiguration(
                target=dependency,
                mock_type='module',
                return_value=None,
                side_effect=None,
                attributes={},
                call_count=None,
                call_args=[]
            )
        
        return None
    
    def _create_mock_by_strategy(self, dependency: str, config: Dict[str, str]) -> MockConfiguration:
        """根据策略创建 Mock 配置"""
        strategy = config['mock_strategy']
        dep_type = config['type']
        
        if strategy == 'connection':
            # 数据库连接 Mock
            return MockConfiguration(
                target=dependency,
                mock_type='class',
                return_value='mock_connection',
                side_effect=None,
                attributes={
                    'cursor.return_value': 'mock_cursor',
                    'commit.return_value': None,
                    'rollback.return_value': None,
                    'close.return_value': None
                },
                call_count=None,
                call_args=[]
            )
        
        elif strategy == 'http_client':
            # HTTP 客户端 Mock
            return MockConfiguration(
                target=dependency,
                mock_type='module',
                return_value=None,
                side_effect=None,
                attributes={
                    'get.return_value.status_code': 200,
                    'get.return_value.json.return_value': {'status': 'success'},
                    'post.return_value.status_code': 201,
                    'put.return_value.status_code': 200,
                    'delete.return_value.status_code': 204
                },
                call_count=None,
                call_args=[]
            )
        
        elif strategy == 'file_operations':
            # 文件操作 Mock
            return MockConfiguration(
                target=dependency,
                mock_type='module',
                return_value=None,
                side_effect=None,
                attributes={
                    'copy.return_value': None,
                    'move.return_value': None,
                    'rmtree.return_value': None,
                    'copytree.return_value': None
                },
                call_count=None,
                call_args=[]
            )
        
        elif strategy == 'datetime_operations':
            # 时间操作 Mock
            return MockConfiguration(
                target=f"{dependency}.datetime.now",
                mock_type='function',
                return_value='datetime(2023, 1, 1, 12, 0, 0)',
                side_effect=None,
                attributes={},
                call_count=None,
                call_args=[]
            )
        
        else:
            # 默认 Mock 配置
            return MockConfiguration(
                target=dependency,
                mock_type='module',
                return_value=None,
                side_effect=None,
                attributes={},
                call_count=None,
                call_args=[]
            )
    
    def _is_external_dependency(self, dependency: str) -> bool:
        """判断是否是外部依赖"""
        # 简单的启发式规则
        internal_patterns = ['workflow', 'tests', '__main__', '__init__']

        for pattern in internal_patterns:
            if dependency.startswith(pattern):
                return False

        # 常见的标准库模块（不需要 Mock）
        stdlib_modules = {
            'sys', 'os', 'json', 'csv', 'math', 'random', 'collections',
            'itertools', 'functools', 'operator', 're', 'string', 'io',
            'typing', 'dataclasses', 'enum', 'abc', 'contextlib'
        }

        # 获取顶级模块名
        top_level_module = dependency.split('.')[0]

        # 如果是标准库模块，仍然可能需要 Mock（如 os, datetime 等）
        # 但不是所有标准库都需要
        if top_level_module in stdlib_modules:
            return False

        # 其他情况都认为是外部依赖
        return True

    def generate_fixture_configurations(self, source_files: List[str], mock_configs: Dict[str, List[MockConfiguration]]) -> Dict[str, List[FixtureConfiguration]]:
        """
        生成 Fixture 配置

        Args:
            source_files: 源代码文件列表
            mock_configs: Mock 配置

        Returns:
            Dict[str, List[FixtureConfiguration]]: 文件到 Fixture 配置列表的映射
        """
        file_fixtures = {}

        for source_file in source_files:
            fixtures = []

            # 为每个文件生成基础 fixture
            fixtures.extend(self._generate_basic_fixtures(source_file))

            # 根据 Mock 配置生成相关 fixture
            if source_file in mock_configs:
                fixtures.extend(self._generate_mock_fixtures(mock_configs[source_file]))

            # 生成数据 fixture
            fixtures.extend(self._generate_data_fixtures(source_file))

            file_fixtures[source_file] = fixtures

        return file_fixtures

    def _generate_basic_fixtures(self, source_file: str) -> List[FixtureConfiguration]:
        """生成基础 fixture"""
        fixtures = []

        # 临时目录 fixture
        fixtures.append(FixtureConfiguration(
            name="temp_dir",
            scope="function",
            params=None,
            setup_code="""import tempfile
import shutil
temp_dir = tempfile.mkdtemp()
yield temp_dir
shutil.rmtree(temp_dir)""",
            teardown_code="",
            dependencies=[],
            data_type="filesystem"
        ))

        # 测试数据目录 fixture
        fixtures.append(FixtureConfiguration(
            name="test_data_dir",
            scope="session",
            params=None,
            setup_code="""from pathlib import Path
test_data_dir = Path(__file__).parent / "fixtures" / "data"
test_data_dir.mkdir(parents=True, exist_ok=True)
return test_data_dir""",
            teardown_code="",
            dependencies=[],
            data_type="filesystem"
        ))

        return fixtures

    def _generate_mock_fixtures(self, mock_configs: List[MockConfiguration]) -> List[FixtureConfiguration]:
        """根据 Mock 配置生成相关 fixture"""
        fixtures = []

        for mock_config in mock_configs:
            if mock_config.mock_type == 'database':
                fixtures.append(self._create_database_fixture(mock_config))
            elif mock_config.mock_type == 'network':
                fixtures.append(self._create_network_fixture(mock_config))
            elif mock_config.mock_type == 'filesystem':
                fixtures.append(self._create_filesystem_fixture(mock_config))

        return fixtures

    def _generate_data_fixtures(self, source_file: str) -> List[FixtureConfiguration]:
        """生成数据 fixture"""
        fixtures = []

        # 示例数据 fixture
        fixtures.append(FixtureConfiguration(
            name="sample_data",
            scope="function",
            params=[
                {"id": 1, "name": "test_item_1", "value": 100},
                {"id": 2, "name": "test_item_2", "value": 200},
                {"id": 3, "name": "test_item_3", "value": 300}
            ],
            setup_code="return request.param",
            teardown_code="",
            dependencies=[],
            data_type="object"
        ))

        # JSON 测试数据 fixture
        fixtures.append(FixtureConfiguration(
            name="json_test_data",
            scope="session",
            params=None,
            setup_code="""import json
test_data = {
    "users": [
        {"id": 1, "name": "Alice", "email": "<EMAIL>"},
        {"id": 2, "name": "Bob", "email": "<EMAIL>"}
    ],
    "settings": {
        "debug": True,
        "max_connections": 100
    }
}
return test_data""",
            teardown_code="",
            dependencies=[],
            data_type="object"
        ))

        return fixtures

    def _create_database_fixture(self, mock_config: MockConfiguration) -> FixtureConfiguration:
        """创建数据库相关 fixture"""
        return FixtureConfiguration(
            name="mock_database",
            scope="function",
            params=None,
            setup_code=f"""from unittest.mock import Mock, patch
with patch('{mock_config.target}') as mock_db:
    mock_connection = Mock()
    mock_cursor = Mock()
    mock_connection.cursor.return_value = mock_cursor
    mock_db.connect.return_value = mock_connection
    yield mock_db, mock_connection, mock_cursor""",
            teardown_code="",
            dependencies=[],
            data_type="database"
        )

    def _create_network_fixture(self, mock_config: MockConfiguration) -> FixtureConfiguration:
        """创建网络相关 fixture"""
        return FixtureConfiguration(
            name="mock_http_client",
            scope="function",
            params=None,
            setup_code=f"""from unittest.mock import Mock, patch
with patch('{mock_config.target}') as mock_requests:
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.json.return_value = {{'status': 'success'}}
    mock_requests.get.return_value = mock_response
    mock_requests.post.return_value = mock_response
    yield mock_requests""",
            teardown_code="",
            dependencies=[],
            data_type="network"
        )

    def _create_filesystem_fixture(self, mock_config: MockConfiguration) -> FixtureConfiguration:
        """创建文件系统相关 fixture"""
        return FixtureConfiguration(
            name="mock_filesystem",
            scope="function",
            params=None,
            setup_code=f"""from unittest.mock import Mock, patch
with patch('{mock_config.target}') as mock_fs:
    yield mock_fs""",
            teardown_code="",
            dependencies=[],
            data_type="filesystem"
        )

    def generate_mock_code(self, mock_configs: List[MockConfiguration]) -> str:
        """
        生成 Mock 代码

        Args:
            mock_configs: Mock 配置列表

        Returns:
            str: 生成的 Mock 代码
        """
        if not mock_configs:
            return ""

        mock_imports = set()
        mock_setups = []

        for config in mock_configs:
            # 添加必要的导入
            mock_imports.add("from unittest.mock import Mock, patch, MagicMock")

            # 生成 Mock 设置代码
            if config.mock_type == 'function':
                mock_setup = f"""
@patch('{config.target}')
def mock_{config.target.replace('.', '_')}(mock_func):
    mock_func.return_value = {config.return_value or 'None'}
    return mock_func"""

            elif config.mock_type == 'class':
                mock_setup = f"""
@patch('{config.target}')
def mock_{config.target.replace('.', '_')}(mock_class):
    mock_instance = Mock()"""

                # 添加属性配置
                for attr, value in config.attributes.items():
                    mock_setup += f"\n    mock_instance.{attr} = {value}"

                mock_setup += f"\n    mock_class.return_value = mock_instance\n    return mock_class"

            elif config.mock_type == 'module':
                mock_setup = f"""
@patch('{config.target}')
def mock_{config.target.replace('.', '_')}(mock_module):"""

                # 添加属性配置
                for attr, value in config.attributes.items():
                    mock_setup += f"\n    mock_module.{attr} = {value}"

                mock_setup += f"\n    return mock_module"

            mock_setups.append(mock_setup)

        # 组合代码
        imports_code = "\n".join(sorted(mock_imports))
        setups_code = "\n".join(mock_setups)

        return f"{imports_code}\n\n{setups_code}"

    def generate_fixture_code(self, fixture_configs: List[FixtureConfiguration]) -> str:
        """
        生成 Fixture 代码

        Args:
            fixture_configs: Fixture 配置列表

        Returns:
            str: 生成的 Fixture 代码
        """
        if not fixture_configs:
            return ""

        fixture_imports = set()
        fixture_codes = []

        fixture_imports.add("import pytest")

        for config in fixture_configs:
            # 生成 fixture 装饰器
            decorator = f"@pytest.fixture"
            if config.scope != "function":
                decorator += f"(scope='{config.scope}')"

            if config.params:
                decorator += f"(params={config.params})"
                decorator = f"@pytest.fixture(scope='{config.scope}', params={config.params})"

            # 生成 fixture 函数
            # 确保 setup_code 有正确的缩进
            indented_setup_code = '\n'.join(['    ' + line if line.strip() else line
                                           for line in config.setup_code.split('\n')])

            fixture_func = f"""
{decorator}
def {config.name}({', '.join(['request'] if config.params else config.dependencies)}):
    \"\"\"Auto-generated fixture for {config.data_type} testing\"\"\"
{indented_setup_code}"""

            if config.teardown_code:
                fixture_func += f"\n    # Teardown\n    {config.teardown_code}"

            fixture_codes.append(fixture_func)

        # 组合代码
        imports_code = "\n".join(sorted(fixture_imports))
        fixtures_code = "\n".join(fixture_codes)

        return f"{imports_code}\n{fixtures_code}"

    def generate_conftest_py(self, fixture_configs: List[FixtureConfiguration], mock_configs: List[MockConfiguration]) -> str:
        """
        生成 conftest.py 文件内容

        Args:
            fixture_configs: Fixture 配置列表
            mock_configs: Mock 配置列表

        Returns:
            str: conftest.py 文件内容
        """
        imports = [
            "import pytest",
            "import tempfile",
            "import shutil",
            "from pathlib import Path",
            "from unittest.mock import Mock, patch, MagicMock"
        ]

        # 生成 fixture 代码
        fixture_code = self.generate_fixture_code(fixture_configs)

        # 生成 Mock 代码（作为 fixture）
        mock_fixture_code = ""
        if mock_configs:
            mock_fixture_code = "\n\n# Mock fixtures\n"
            for config in mock_configs:
                mock_fixture_code += f"""
@pytest.fixture
def mock_{config.target.replace('.', '_')}():
    with patch('{config.target}') as mock_obj:
        # Configure mock attributes
        for attr, value in {config.attributes}.items():
            setattr(mock_obj, attr, value)
        yield mock_obj
"""

        return f"""# Auto-generated conftest.py
# This file contains shared fixtures and mock configurations

{chr(10).join(imports)}

{fixture_code}
{mock_fixture_code}
"""
