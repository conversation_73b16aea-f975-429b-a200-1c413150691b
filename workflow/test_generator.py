"""
Template-driven TestGenerator

此模块实现 TestGenerator，使用 TemplateManager 来渲染测试文件模版（位于
workflow/templates/tests/python/ 下）。实现保留原有接口以兼容现有单元测试，
并在生成测试文件时优先使用模板渲染，回退到内联拼接策略（以确保健壮性）。
"""

from pathlib import Path
import time
import logging
import subprocess
from typing import List, Dict, Any, Optional

from .models import TestGenerationResult, TestCase
from .template_manager import TemplateManager
from .code_generator import CodeGenerator
from .coverage_analyzer import CoverageAnalyzer, CoverageReport

logger = logging.getLogger(__name__)


class TestGenerator:
    """基于模板的测试生成器"""

    def __init__(self, project_root: str, code_generator: Optional[CodeGenerator] = None, template_root: Optional[str] = None, coverage_target: float = 0.9):
        """
        Args:
            project_root: 项目根目录
            code_generator: 可选的 CodeGenerator 实例（用于复用分析方法）
            template_root: 模板根目录，默认为 workflow/templates
            coverage_target: 覆盖率目标，默认90%
        """
        self.project_root = Path(project_root)
        self.code_generator = code_generator or CodeGenerator(project_root)
        self.template_root = template_root or str(self.project_root / "workflow" / "templates")
        self.template_manager = TemplateManager(self.template_root)
        self.coverage_analyzer = CoverageAnalyzer(project_root, coverage_target)

        # 测试文件输出目录
        self.test_output_dirs = {
            'unit': self.project_root / 'tests' / 'unit',
            'integration': self.project_root / 'tests' / 'integration'
        }
        for d in self.test_output_dirs.values():
            d.mkdir(parents=True, exist_ok=True)

        logger.info(f"TestGenerator initialized with project_root: {self.project_root}, templates: {self.template_root}")

    def generate_tests(self, code_files: List[str]) -> TestGenerationResult:
        start_time = time.time()
        test_files: List[str] = []
        test_cases: List[TestCase] = []
        errors: List[str] = []
        warnings: List[str] = []
        total_test_count = 0

        for code_file in code_files:
            try:
                analysis = self._analyze_source_file(code_file)
                file_test_cases = self._generate_test_cases(code_file, analysis)
                test_cases.extend(file_test_cases)

                test_file_path = self._create_test_file(code_file, file_test_cases)
                if test_file_path:
                    test_files.append(test_file_path)
                    total_test_count += len(file_test_cases)

            except Exception as e:
                msg = f"Failed to generate tests for {code_file}: {e}"
                logger.error(msg)
                errors.append(msg)

        quality_score = self._calculate_quality_score(test_cases, errors)
        execution_results = {}
        coverage_report = {}

        if test_files:
            execution_results = self._execute_tests(test_files)

        if test_files and not errors:
            try:
                coverage_analysis = self.coverage_analyzer.analyze_coverage(code_files, test_files)
                coverage_report = {
                    'total_coverage': coverage_analysis.total_coverage,
                    'file_coverage': coverage_analysis.file_coverage,
                    'missing_lines': coverage_analysis.missing_lines,
                    'uncovered_functions': coverage_analysis.uncovered_functions,
                    'branch_coverage': coverage_analysis.branch_coverage,
                    'target_met': coverage_analysis.target_met,
                    'recommendations': coverage_analysis.recommendations
                }
            except Exception as e:
                warnings.append(f"Coverage generation failed: {e}")

        result = TestGenerationResult(
            success=len(errors) == 0 and total_test_count > 0,
            test_files=test_files,
            coverage_report=coverage_report,
            test_count=total_test_count,
            quality_score=quality_score,
            execution_results=execution_results,
            errors=errors,
            warnings=warnings
        )

        logger.info(f"Test generation finished. files={len(test_files)} tests={total_test_count} score={quality_score:.2f}")
        return result

    def generate_additional_tests_for_coverage(self, source_files: List[str], existing_test_files: List[str]) -> TestGenerationResult:
        """
        基于覆盖率分析生成额外的测试用例

        Args:
            source_files: 源代码文件列表
            existing_test_files: 现有测试文件列表

        Returns:
            TestGenerationResult: 额外测试生成结果
        """
        logger.info("Generating additional tests based on coverage analysis")

        # 分析当前覆盖率
        coverage_analysis = self.coverage_analyzer.analyze_coverage(source_files, existing_test_files)

        if coverage_analysis.target_met:
            logger.info(f"Coverage target already met: {coverage_analysis.total_coverage*100:.1f}%")
            return TestGenerationResult(
                success=True,
                test_files=[],
                coverage_report={'total_coverage': coverage_analysis.total_coverage},
                test_count=0,
                quality_score=1.0,
                execution_results={},
                errors=[],
                warnings=["Coverage target already met - no additional tests needed"]
            )

        # 为未覆盖的函数生成测试
        additional_test_cases = []
        for file_name, functions in coverage_analysis.uncovered_functions.items():
            for func_name in functions:
                # 生成边界测试和异常测试
                edge_cases = self._generate_edge_case_tests(file_name, func_name)
                additional_test_cases.extend(edge_cases)

        # 创建额外的测试文件
        additional_test_files = []
        if additional_test_cases:
            for source_file in source_files:
                file_test_cases = [tc for tc in additional_test_cases if tc.target_function.startswith(source_file)]
                if file_test_cases:
                    test_file_path = self._create_additional_test_file(source_file, file_test_cases)
                    if test_file_path:
                        additional_test_files.append(test_file_path)

        # 执行新测试并重新分析覆盖率
        execution_results = {}
        if additional_test_files:
            execution_results = self._execute_tests(additional_test_files)

            # 重新分析覆盖率
            all_test_files = existing_test_files + additional_test_files
            final_coverage = self.coverage_analyzer.analyze_coverage(source_files, all_test_files)

            coverage_report = {
                'total_coverage': final_coverage.total_coverage,
                'file_coverage': final_coverage.file_coverage,
                'target_met': final_coverage.target_met,
                'improvement': final_coverage.total_coverage - coverage_analysis.total_coverage
            }
        else:
            coverage_report = {'total_coverage': coverage_analysis.total_coverage}

        return TestGenerationResult(
            success=len(additional_test_files) > 0,
            test_files=additional_test_files,
            coverage_report=coverage_report,
            test_count=len(additional_test_cases),
            quality_score=self._calculate_quality_score(additional_test_cases, []),
            execution_results=execution_results,
            errors=[],
            warnings=[]
        )

    def _generate_edge_case_tests(self, file_name: str, func_name: str) -> List[TestCase]:
        """为函数生成边界测试用例"""
        edge_cases = []

        # 生成空值测试
        edge_cases.append(TestCase(
            name=f"test_{func_name}_with_none",
            target_function=func_name,
            test_type="edge_case",
            arrange_code="# Test with None values\ntest_input = None",
            act_code=f"# Should handle None gracefully\ntry:\n    result = {func_name}(test_input)\nexcept Exception as e:\n    result = e",
            assert_code="# Verify proper None handling\nassert result is not None or isinstance(result, Exception)",
            mock_requirements=[],
            fixtures=[]
        ))

        # 生成边界值测试
        edge_cases.append(TestCase(
            name=f"test_{func_name}_boundary_values",
            target_function=func_name,
            test_type="edge_case",
            arrange_code="# Test with boundary values\nmin_val = 0\nmax_val = 999999",
            act_code=f"# Test boundary conditions\nresult_min = {func_name}(min_val)\nresult_max = {func_name}(max_val)",
            assert_code="# Verify boundary value handling\nassert result_min is not None\nassert result_max is not None",
            mock_requirements=[],
            fixtures=[]
        ))

        # 生成异常测试
        edge_cases.append(TestCase(
            name=f"test_{func_name}_exception_handling",
            target_function=func_name,
            test_type="exception",
            arrange_code="# Test exception scenarios\ninvalid_input = 'invalid'",
            act_code=f"# Should raise appropriate exception\nwith pytest.raises(Exception):\n    {func_name}(invalid_input)",
            assert_code="# Exception test - assertion in act phase",
            mock_requirements=[],
            fixtures=[]
        ))

        return edge_cases

    def _create_additional_test_file(self, source_file: str, test_cases: List[TestCase]) -> Optional[str]:
        """创建额外的测试文件"""
        if not test_cases:
            return None

        try:
            source_path = Path(source_file)
            test_filename = f"test_{source_path.stem}_additional.py"
            test_file_path = self.test_output_dirs['unit'] / test_filename

            content = self._generate_test_file_content(source_file, test_cases)
            with open(test_file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            logger.info(f"Created additional test file: {test_file_path}")
            return str(test_file_path.relative_to(self.project_root))

        except Exception as e:
            logger.error(f"Failed to create additional test file for {source_file}: {e}")
            return None

    def _analyze_source_file(self, file_path: str) -> Dict[str, Any]:
        return self.code_generator.analyze_code_structure(file_path)

    def _generate_test_cases(self, source_file: str, analysis: Dict[str, Any]) -> List[TestCase]:
        cases: List[TestCase] = []
        for cls in analysis.get('classes', []):
            cases.extend(self._generate_class_tests(source_file, cls))
        for func in analysis.get('functions', []):
            cases.extend(self._generate_function_tests(source_file, func))
        return cases

    def _generate_class_tests(self, source_file: str, class_info: Dict[str, Any]) -> List[TestCase]:
        cases: List[TestCase] = []
        class_name = class_info.get('name')
        for m in class_info.get('methods', []):
            if not m.get('name', '').startswith('_'):
                cases.extend(self._generate_method_tests(source_file, class_name, m))
        return cases

    def _generate_function_tests(self, source_file: str, func_info: Dict[str, Any]) -> List[TestCase]:
        func_name = func_info.get('name')
        args = func_info.get('args', [])
        tc = TestCase(
            name=f"test_{func_name}_basic",
            target_function=func_name,
            test_type="unit",
            arrange_code=self._generate_arrange_code(func_name, args, is_method=False),
            act_code=self._generate_act_code(func_name, args, is_method=False),
            assert_code=self._generate_assert_code(func_name),
            mock_requirements=self._identify_mock_requirements(source_file, func_info),
            fixtures=[]
        )
        return [tc]

    def _generate_method_tests(self, source_file: str, class_name: str, method_info: Dict[str, Any]) -> List[TestCase]:
        method_name = method_info.get('name')
        args = method_info.get('args', [])
        tc = TestCase(
            name=f"test_{class_name.lower()}_{method_name}_basic",
            target_function=f"{class_name}.{method_name}",
            test_type="unit",
            arrange_code=self._generate_arrange_code(method_name, args, is_method=True, class_name=class_name),
            act_code=self._generate_act_code(method_name, args, is_method=True, class_name=class_name),
            assert_code=self._generate_assert_code(method_name),
            mock_requirements=self._identify_mock_requirements(source_file, method_info),
            fixtures=[]
        )
        return [tc]

    def _generate_arrange_code(self, func_name: str, args: List[str], is_method: bool = False, class_name: Optional[str] = None) -> str:
        if is_method and class_name:
            lines = [f"instance = {class_name}()"]
            for arg in args:
                if arg != 'self':
                    lines.append(f"{arg} = None  # TODO: Provide test data")
        else:
            lines = []
            for arg in args:
                lines.append(f"{arg} = None  # TODO: Provide test data")
        return "\n".join(lines)

    def _generate_act_code(self, func_name: str, args: List[str], is_method: bool = False, class_name: Optional[str] = None) -> str:
        lines = []
        if is_method and class_name:
            method_args = [a for a in args if a != 'self']
            if method_args:
                args_str = ", ".join(method_args)
                lines.append(f"result = instance.{func_name}({args_str})")
            else:
                lines.append(f"result = instance.{func_name}()")
        else:
            if args:
                args_str = ", ".join(args)
                lines.append(f"result = {func_name}({args_str})")
            else:
                lines.append(f"result = {func_name}()")
        return "\n".join(lines)

    def _generate_assert_code(self, func_name: str) -> str:
        return "\n".join([
            "assert result is not None  # TODO: Add specific assertions",
            "# TODO: Add more specific test assertions based on expected behavior"
        ])

    def _identify_mock_requirements(self, source_file: str, func_info: Dict[str, Any]) -> List[str]:
        mock_requirements: List[str] = []
        common_mock_targets = {
            'os': 'os operations',
            'subprocess': 'subprocess calls',
            'pathlib.Path': 'file system operations',
            'datetime.datetime': 'time operations',
            'logging': 'logging operations',
            'sqlite3': 'database operations'
        }
        try:
            full_path = self.project_root / source_file
            content = full_path.read_text(encoding='utf-8')
            for module in common_mock_targets.keys():
                if module in content:
                    mock_requirements.append(module)
        except Exception:
            logger.debug(f"Could not inspect {source_file} for mocks")
        return mock_requirements

    def _create_test_file(self, source_file: str, test_cases: List[TestCase]) -> Optional[str]:
        if not test_cases:
            return None
        try:
            source_path = Path(source_file)
            test_filename = f"test_{source_path.stem}.py"
            test_file_path = self.test_output_dirs['unit'] / test_filename
            content = self._generate_test_file_content(source_file, test_cases)
            with open(test_file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"Created test file: {test_file_path}")
            return str(test_file_path.relative_to(self.project_root))
        except Exception as e:
            logger.error(f"Failed to write test file for {source_file}: {e}")
            return None

    def _generate_test_file_content(self, source_file: str, test_cases: List[TestCase]) -> str:
        """
        首先尝试使用模板渲染：
          template_name: unit_test (workflow/templates/tests/python/unit_test.py.j2)
          parameters:
            - module_name
            - tests (list of test dicts: name, arrange, act, assert, mocks)
        回退方案使用内联拼接实现，保证生成可靠。
        """
        module_name = source_file.replace('/', '.').replace('.py', '')
        tests_for_template = []
        for tc in test_cases:
            tests_for_template.append({
                'name': tc.name,
                'arrange': tc.arrange_code,
                'act': tc.act_code,
                'assert': tc.assert_code,
                'mocks': tc.mock_requirements or []
            })

        # Attempt template rendering
        try:
            # 使用正确的模板路径：tests/python/unit_test.py.j2
            template_path = "tests/python/unit_test.py.j2"
            template = self.template_manager.jinja_env.get_template(template_path)

            # 准备模板参数
            template_params = {
                'module_name': module_name,
                'imports': [f"from {module_name} import *"],
                'test_cases': [
                    {
                        'name': tc.name,
                        'docstring': f"Test {tc.target_function} - {tc.test_type}",
                        'arrange_code': tc.arrange_code,
                        'act_code': tc.act_code,
                        'assert_code': tc.assert_code,
                        'mock_requirements': tc.mock_requirements or []
                    } for tc in test_cases
                ]
            }

            # 渲染模板
            rendered = template.render(**template_params)
            return rendered
        except Exception as e:
            logger.debug(f"Template rendering failed for {source_file}: {e} - falling back to string assembly")

        # Fallback: simple assembly
        imports = [
            "import pytest",
            "from unittest.mock import Mock, patch",
            f"from {module_name} import *",
            ""
        ]
        test_methods = [self._generate_test_method(tc) for tc in test_cases]
        parts = [
            '"""',
            f'Unit tests for {source_file}',
            'Generated automatically by TestGenerator',
            '"""',
            "",
            "\n".join(imports),
            "\n\n".join(test_methods)
        ]
        return "\n".join(parts)

    def _generate_test_method(self, test_case: TestCase) -> str:
        method_lines: List[str] = []
        # Add mock decorators if any
        if test_case.mock_requirements:
            for m in test_case.mock_requirements:
                method_lines.append(f'@patch("{m}")')
        method_lines.append(f"def {test_case.name}():")
        method_lines.append(f'    """Test {test_case.target_function} - {test_case.test_type}"""')
        if test_case.arrange_code:
            for line in test_case.arrange_code.splitlines():
                method_lines.append(f"    {line}")
        if test_case.act_code:
            for line in test_case.act_code.splitlines():
                method_lines.append(f"    {line}")
        if test_case.assert_code:
            for line in test_case.assert_code.splitlines():
                method_lines.append(f"    {line}")
        return "\n".join(method_lines)

    def _calculate_quality_score(self, test_cases: List[TestCase], errors: List[str]) -> float:
        if not test_cases:
            return 0.0
        base_score = 0.5
        test_count_score = min(len(test_cases) / 10.0, 0.3)
        error_penalty = min(len(errors) * 0.1, 0.4)
        has_assertions = sum(1 for tc in test_cases if "assert" in tc.assert_code) / len(test_cases) * 0.2
        final = base_score + test_count_score + has_assertions - error_penalty
        return max(0.0, min(1.0, final))

    def _execute_tests(self, test_files: List[str]) -> Dict[str, Any]:
        results = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'errors': [],
            'warnings': [],
            'execution_time': 0.0
        }
        try:
            test_paths = [str(self.project_root / tf) for tf in test_files]
            cmd = ['python', '-m', 'pytest', '-v', '--tb=short'] + test_paths
            start = time.time()
            proc = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            results['execution_time'] = time.time() - start
            output = proc.stdout + proc.stderr
            results['passed'] = output.count(' PASSED')
            results['failed'] = output.count(' FAILED')
            results['total_tests'] = results['passed'] + results['failed']
            if proc.returncode != 0:
                results['errors'].append(proc.stderr or "pytest returned non-zero")
        except subprocess.TimeoutExpired:
            results['errors'].append("Test execution timed out")
        except FileNotFoundError:
            results['errors'].append("pytest not found - please install pytest")
        except Exception as e:
            results['errors'].append(str(e))
        return results

    def _generate_coverage_report(self, source_files: List[str], test_files: List[str]) -> Dict[str, Any]:
        # Delegate to TemplateManager/CodeGenerator helpers if available or use coverage CLI
        try:
            # use coverage CLI similar to previous implementation
            test_paths = [str(self.project_root / tf) for tf in test_files]
            source_paths = [str(self.project_root / sf) for sf in source_files]
            cmd = ['python', '-m', 'coverage', 'run', '--source'] + source_paths + ['-m', 'pytest'] + test_paths
            proc = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            if proc.returncode != 0:
                return {'errors': ['Failed to run coverage'], 'total_coverage': 0.0}
            report = subprocess.run(['python', '-m', 'coverage', 'report', '--show-missing'], capture_output=True, text=True, timeout=30)
            if report.returncode != 0:
                return {'errors': ['Failed to generate coverage report'], 'total_coverage': 0.0}
            # parse minimal total coverage
            lines = report.stdout.splitlines()
            total = 0.0
            for line in lines:
                if 'TOTAL' in line:
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            total = float(parts[3].replace('%', '')) / 100.0
                        except Exception:
                            total = 0.0
            return {'total_coverage': total, 'file_coverage': {}}
        except Exception as e:
            return {'errors': [str(e)], 'total_coverage': 0.0}