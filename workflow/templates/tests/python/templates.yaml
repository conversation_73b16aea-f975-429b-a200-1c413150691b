templates:
  unit_test:
    description: "Python单元测试模板，使用pytest框架和AAA模式"
    file_extension: ".py"
    default_parameters:
      imports:
        - "import pytest"
        - "from unittest.mock import Mock, patch, MagicMock"
      mock_requirements: []
      class_name: null
      constructor_args: ""
      test_cases: []
      additional_tests: {}
    required_parameters:
      - "test_cases"
    
  integration_test:
    description: "Python集成测试模板，测试组件交互和系统行为"
    file_extension: ".py"
    default_parameters:
      imports:
        - "import pytest"
        - "import asyncio"
        - "from unittest.mock import Mock, patch, MagicMock"
      class_name: null
      setup_code: ""
      cleanup_code: ""
      test_cases: []
      database_tests: {}
      api_tests: {}
      api_client_setup: ""
    required_parameters:
      - "test_cases"
      
  mock_setup:
    description: "Mock对象设置模板，用于测试中的依赖模拟"
    file_extension: ".py"
    default_parameters:
      mock_targets: []
      mock_configurations: {}
      fixture_name: "mock_setup"
    required_parameters:
      - "mock_targets"
      
  fixture:
    description: "Pytest fixture模板，用于测试数据和环境设置"
    file_extension: ".py"
    default_parameters:
      fixture_name: "test_fixture"
      fixture_scope: "function"
      setup_code: ""
      cleanup_code: ""
      return_value: "test_data"
    required_parameters:
      - "fixture_name"
