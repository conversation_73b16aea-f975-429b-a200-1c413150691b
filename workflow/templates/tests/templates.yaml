templates:
  conftest:
    description: "Pytest配置文件模板，定义全局fixtures和配置"
    file_extension: ".py"
    default_parameters:
      project_name: "Test Project"
      fixtures: []
      pytest_plugins: []
      test_markers: []
    required_parameters: []
    
  test_config:
    description: "测试配置模板，定义测试环境和参数"
    file_extension: ".py"
    default_parameters:
      test_database_url: "sqlite:///:memory:"
      test_data_dir: "tests/fixtures"
      mock_external_services: true
      log_level: "INFO"
    required_parameters: []
