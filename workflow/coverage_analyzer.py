"""
Coverage Analyzer for Test Generation

此模块实现测试覆盖率分析功能，包括：
- 代码覆盖率收集和分析
- 缺失覆盖率识别
- 覆盖率报告生成
- 覆盖率目标验证
"""

import ast
import json
import logging
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class CoverageReport:
    """覆盖率报告数据结构"""
    total_coverage: float
    file_coverage: Dict[str, float]
    missing_lines: Dict[str, List[int]]
    uncovered_functions: Dict[str, List[str]]
    branch_coverage: Dict[str, float]
    target_met: bool
    recommendations: List[str]
    raw_data: Dict[str, Any]


@dataclass
class CodePath:
    """代码路径分析结果"""
    file_path: str
    function_name: str
    line_start: int
    line_end: int
    complexity: int
    branches: List[int]
    is_covered: bool


class CoverageAnalyzer:
    """测试覆盖率分析器"""
    
    def __init__(self, project_root: str, coverage_target: float = 0.9):
        """
        Args:
            project_root: 项目根目录
            coverage_target: 覆盖率目标（默认90%）
        """
        self.project_root = Path(project_root)
        self.coverage_target = coverage_target
        self.coverage_data_file = self.project_root / ".coverage"
        
        logger.info(f"CoverageAnalyzer initialized with target: {coverage_target*100}%")
    
    def analyze_coverage(self, source_files: List[str], test_files: List[str]) -> CoverageReport:
        """
        分析测试覆盖率
        
        Args:
            source_files: 源代码文件列表
            test_files: 测试文件列表
            
        Returns:
            CoverageReport: 覆盖率分析报告
        """
        logger.info(f"Starting coverage analysis for {len(source_files)} source files")
        
        # 运行覆盖率收集
        coverage_data = self._run_coverage_collection(source_files, test_files)
        if not coverage_data.get('success', False):
            return self._create_error_report(coverage_data.get('errors', []))
        
        # 解析覆盖率数据
        report_data = self._parse_coverage_report()
        
        # 分析代码路径
        code_paths = self._analyze_code_paths(source_files)
        
        # 识别缺失覆盖率
        missing_coverage = self._identify_missing_coverage(source_files, report_data)
        
        # 生成建议
        recommendations = self._generate_recommendations(report_data, missing_coverage, code_paths)
        
        # 创建覆盖率报告
        report = CoverageReport(
            total_coverage=report_data.get('total_coverage', 0.0),
            file_coverage=report_data.get('file_coverage', {}),
            missing_lines=missing_coverage.get('missing_lines', {}),
            uncovered_functions=missing_coverage.get('uncovered_functions', {}),
            branch_coverage=report_data.get('branch_coverage', {}),
            target_met=report_data.get('total_coverage', 0.0) >= self.coverage_target,
            recommendations=recommendations,
            raw_data=report_data
        )
        
        logger.info(f"Coverage analysis completed: {report.total_coverage*100:.1f}% (target: {self.coverage_target*100}%)")
        return report
    
    def _run_coverage_collection(self, source_files: List[str], test_files: List[str]) -> Dict[str, Any]:
        """运行覆盖率收集"""
        try:
            # 清理之前的覆盖率数据
            if self.coverage_data_file.exists():
                self.coverage_data_file.unlink()
            
            # 准备文件路径
            test_paths = [str(self.project_root / tf) for tf in test_files]

            # 构建覆盖率命令 - 使用项目根目录作为源码路径
            cmd = [
                'python3', '-m', 'coverage', 'run',
                '--source', str(self.project_root),
                '--branch',  # 启用分支覆盖率
                '-m', 'pytest'
            ] + test_paths
            
            logger.debug(f"Running coverage command: {' '.join(cmd)}")
            
            # 执行覆盖率收集
            start_time = time.time()
            proc = subprocess.run(cmd, capture_output=True, text=True, timeout=120, cwd=self.project_root)
            execution_time = time.time() - start_time
            
            if proc.returncode != 0:
                logger.error(f"Coverage collection failed: {proc.stderr}")
                return {
                    'success': False,
                    'errors': [f"Coverage collection failed: {proc.stderr}"],
                    'execution_time': execution_time
                }
            
            return {
                'success': True,
                'execution_time': execution_time,
                'stdout': proc.stdout,
                'stderr': proc.stderr
            }
            
        except subprocess.TimeoutExpired:
            return {'success': False, 'errors': ['Coverage collection timed out']}
        except FileNotFoundError:
            return {'success': False, 'errors': ['coverage tool not found - please install coverage']}
        except Exception as e:
            return {'success': False, 'errors': [f"Coverage collection error: {str(e)}"]}
    
    def _parse_coverage_report(self) -> Dict[str, Any]:
        """解析覆盖率报告"""
        try:
            # 生成 JSON 格式的覆盖率报告
            json_cmd = ['python3', '-m', 'coverage', 'json', '-o', '-']
            proc = subprocess.run(json_cmd, capture_output=True, text=True, timeout=30, cwd=self.project_root)
            
            if proc.returncode != 0:
                logger.warning(f"Failed to generate JSON coverage report: {proc.stderr}")
                return self._parse_text_coverage_report()
            
            # 解析 JSON 数据
            coverage_data = json.loads(proc.stdout)
            
            # 提取关键信息
            total_coverage = coverage_data.get('totals', {}).get('percent_covered', 0.0) / 100.0
            
            file_coverage = {}
            branch_coverage = {}
            for file_path, file_data in coverage_data.get('files', {}).items():
                file_coverage[file_path] = file_data.get('summary', {}).get('percent_covered', 0.0) / 100.0
                branch_coverage[file_path] = file_data.get('summary', {}).get('percent_covered_display', '0%')
            
            return {
                'total_coverage': total_coverage,
                'file_coverage': file_coverage,
                'branch_coverage': branch_coverage,
                'raw_json': coverage_data
            }
            
        except Exception as e:
            logger.warning(f"Failed to parse JSON coverage report: {e}")
            return self._parse_text_coverage_report()
    
    def _parse_text_coverage_report(self) -> Dict[str, Any]:
        """解析文本格式的覆盖率报告（回退方案）"""
        try:
            # 生成文本覆盖率报告
            text_cmd = ['python3', '-m', 'coverage', 'report', '--show-missing']
            proc = subprocess.run(text_cmd, capture_output=True, text=True, timeout=30, cwd=self.project_root)
            
            if proc.returncode != 0:
                return {'total_coverage': 0.0, 'file_coverage': {}, 'errors': ['Failed to generate coverage report']}
            
            # 解析文本报告
            lines = proc.stdout.splitlines()
            total_coverage = 0.0
            file_coverage = {}
            
            for line in lines:
                if 'TOTAL' in line:
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            total_coverage = float(parts[3].replace('%', '')) / 100.0
                        except (ValueError, IndexError):
                            pass
                elif line.strip() and not line.startswith('Name') and not line.startswith('---'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            file_name = parts[0]
                            coverage_pct = float(parts[3].replace('%', '')) / 100.0
                            file_coverage[file_name] = coverage_pct
                        except (ValueError, IndexError):
                            pass
            
            return {
                'total_coverage': total_coverage,
                'file_coverage': file_coverage,
                'branch_coverage': {},
                'raw_text': proc.stdout
            }
            
        except Exception as e:
            logger.error(f"Failed to parse text coverage report: {e}")
            return {'total_coverage': 0.0, 'file_coverage': {}, 'errors': [str(e)]}

    def _analyze_code_paths(self, source_files: List[str]) -> List[CodePath]:
        """分析代码路径和复杂度"""
        code_paths = []

        for source_file in source_files:
            try:
                file_path = self.project_root / source_file
                if not file_path.exists():
                    continue

                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 使用 AST 分析代码结构
                tree = ast.parse(content)

                for node in ast.walk(tree):
                    if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                        complexity = self._calculate_complexity(node)
                        branches = self._find_branches(node)

                        code_path = CodePath(
                            file_path=source_file,
                            function_name=node.name,
                            line_start=node.lineno,
                            line_end=getattr(node, 'end_lineno', node.lineno),
                            complexity=complexity,
                            branches=branches,
                            is_covered=False  # 将在后续步骤中更新
                        )
                        code_paths.append(code_path)

            except Exception as e:
                logger.warning(f"Failed to analyze code paths in {source_file}: {e}")

        return code_paths

    def _calculate_complexity(self, node: ast.AST) -> int:
        """计算代码复杂度（简化版）"""
        complexity = 1  # 基础复杂度

        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.Try):
                complexity += len(child.handlers)
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1

        return complexity

    def _find_branches(self, node: ast.AST) -> List[int]:
        """查找分支行号"""
        branches = []

        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor, ast.Try)):
                branches.append(child.lineno)

        return branches

    def _identify_missing_coverage(self, source_files: List[str], report_data: Dict[str, Any]) -> Dict[str, Any]:
        """识别缺失的覆盖率"""
        missing_lines = {}
        uncovered_functions = {}

        try:
            # 获取详细的缺失行信息
            missing_cmd = ['python3', '-m', 'coverage', 'report', '--show-missing']
            proc = subprocess.run(missing_cmd, capture_output=True, text=True, timeout=30, cwd=self.project_root)

            if proc.returncode == 0:
                lines = proc.stdout.splitlines()
                current_file = None

                for line in lines:
                    if line.strip() and not line.startswith('Name') and not line.startswith('---') and 'TOTAL' not in line:
                        parts = line.split()
                        if len(parts) >= 5:
                            file_name = parts[0]
                            missing_str = parts[4] if len(parts) > 4 else ''

                            if missing_str and missing_str != '':
                                missing_line_nums = self._parse_missing_lines(missing_str)
                                if missing_line_nums:
                                    missing_lines[file_name] = missing_line_nums

                                    # 识别未覆盖的函数
                                    uncovered_funcs = self._identify_uncovered_functions(file_name, missing_line_nums)
                                    if uncovered_funcs:
                                        uncovered_functions[file_name] = uncovered_funcs

        except Exception as e:
            logger.warning(f"Failed to identify missing coverage: {e}")

        return {
            'missing_lines': missing_lines,
            'uncovered_functions': uncovered_functions
        }

    def _parse_missing_lines(self, missing_str: str) -> List[int]:
        """解析缺失行号字符串"""
        missing_lines = []

        try:
            # 处理类似 "10-15, 20, 25-30" 的格式
            parts = missing_str.split(',')
            for part in parts:
                part = part.strip()
                if '-' in part:
                    start, end = part.split('-')
                    missing_lines.extend(range(int(start), int(end) + 1))
                elif part.isdigit():
                    missing_lines.append(int(part))
        except Exception as e:
            logger.debug(f"Failed to parse missing lines '{missing_str}': {e}")

        return missing_lines

    def _identify_uncovered_functions(self, file_name: str, missing_lines: List[int]) -> List[str]:
        """识别未覆盖的函数"""
        uncovered_functions = []

        try:
            file_path = self.project_root / file_name
            if not file_path.exists():
                return uncovered_functions

            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            tree = ast.parse(content)

            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    func_start = node.lineno
                    func_end = getattr(node, 'end_lineno', func_start)

                    # 检查函数是否有未覆盖的行
                    func_missing_lines = [line for line in missing_lines if func_start <= line <= func_end]
                    if func_missing_lines:
                        uncovered_functions.append(node.name)

        except Exception as e:
            logger.debug(f"Failed to identify uncovered functions in {file_name}: {e}")

        return uncovered_functions

    def _generate_recommendations(self, report_data: Dict[str, Any], missing_coverage: Dict[str, Any], code_paths: List[CodePath]) -> List[str]:
        """生成覆盖率改进建议"""
        recommendations = []

        total_coverage = report_data.get('total_coverage', 0.0)

        if total_coverage < self.coverage_target:
            gap = (self.coverage_target - total_coverage) * 100
            recommendations.append(f"总覆盖率 {total_coverage*100:.1f}% 低于目标 {self.coverage_target*100}%，需要提高 {gap:.1f}%")

        # 分析文件级别的覆盖率
        file_coverage = report_data.get('file_coverage', {})
        low_coverage_files = [(f, c) for f, c in file_coverage.items() if c < 0.8]

        if low_coverage_files:
            recommendations.append("以下文件覆盖率较低，建议优先添加测试：")
            for file_name, coverage in sorted(low_coverage_files, key=lambda x: x[1]):
                recommendations.append(f"  - {file_name}: {coverage*100:.1f}%")

        # 分析未覆盖的函数
        uncovered_functions = missing_coverage.get('uncovered_functions', {})
        if uncovered_functions:
            recommendations.append("以下函数缺少测试覆盖：")
            for file_name, functions in uncovered_functions.items():
                for func_name in functions:
                    recommendations.append(f"  - {file_name}::{func_name}")

        # 分析复杂函数
        complex_functions = [cp for cp in code_paths if cp.complexity > 5]
        if complex_functions:
            recommendations.append("以下复杂函数建议增加更多测试用例：")
            for cp in complex_functions:
                recommendations.append(f"  - {cp.file_path}::{cp.function_name} (复杂度: {cp.complexity})")

        return recommendations

    def _create_error_report(self, errors: List[str]) -> CoverageReport:
        """创建错误报告"""
        return CoverageReport(
            total_coverage=0.0,
            file_coverage={},
            missing_lines={},
            uncovered_functions={},
            branch_coverage={},
            target_met=False,
            recommendations=[f"覆盖率分析失败: {'; '.join(errors)}"],
            raw_data={'errors': errors}
        )
